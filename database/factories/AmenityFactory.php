<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Amenity>
 */
class AmenityFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $amenities = [
            'Lighting' => 'Professional LED lighting system for evening games',
            'Parking' => 'Dedicated parking area for players and spectators',
            'Restrooms' => 'Clean and accessible restroom facilities',
            'Equipment Storage' => 'Secure storage for sports equipment',
            'Scoreboard' => 'Digital scoreboard for game tracking',
            'Spectator Seating' => 'Comfortable seating for spectators',
            'Sound System' => 'Professional audio system for announcements',
            'WiFi' => 'High-speed wireless internet access',
            'Concessions' => 'Food and beverage concession stand',
            'Lockers' => 'Secure locker facilities for players',
            'Showers' => 'Clean shower facilities',
            'First Aid Station' => 'Emergency first aid and medical supplies',
        ];

        $icons = [
            'ri-lightbulb-line',
            'ri-car-line',
            'ri-home-line',
            'ri-archive-line',
            'ri-dashboard-line',
            'ri-armchair-line',
            'ri-volume-up-line',
            'ri-wifi-line',
            'ri-restaurant-line',
            'ri-lock-line',
            'ri-drop-line',
            'ri-heart-pulse-line',
        ];

        // Generate a unique name by combining a base amenity name with a unique suffix
        $baseAmenityName = $this->faker->randomElement(array_keys($amenities));
        $uniqueName = $baseAmenityName.' '.$this->faker->unique()->numberBetween(1, 9999);

        return [
            'name' => $uniqueName,
            'description' => $amenities[$baseAmenityName],
            'icon_class' => $this->faker->randomElement($icons),
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
        ];
    }

    /**
     * Indicate that the amenity is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the amenity is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
