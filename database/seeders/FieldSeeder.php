<?php

namespace Database\Seeders;

use App\Models\Field;
use App\Models\Amenity;
use Illuminate\Database\Seeder;

class FieldSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $fields = [
            [
                'name' => 'Soccer Field A',
                'type' => 'Soccer',
                'description' => 'Full-size soccer field with natural grass surface. Perfect for competitive matches and training.',
                'hourly_rate' => 75.00,
                'capacity' => 22,
                'status' => 'Active',
                'opening_time' => '08:00',
                'closing_time' => '22:00',
                'min_booking_hours' => 1,
                'max_booking_hours' => 8,
                'amenities' => ['Lighting', 'Parking', 'Restrooms', 'Spectator Seating'],
            ],
            [
                'name' => 'Basketball Court 1',
                'type' => 'Basketball',
                'description' => 'Indoor basketball court with professional hardwood flooring and adjustable hoops.',
                'hourly_rate' => 45.00,
                'capacity' => 10,
                'status' => 'Active',
                'opening_time' => '08:00',
                'closing_time' => '22:00',
                'min_booking_hours' => 1,
                'max_booking_hours' => 6,
                'amenities' => ['Lighting', 'Parking', 'Restrooms', 'Sound System'],
            ],
            [
                'name' => 'Tennis Court A',
                'type' => 'Tennis',
                'description' => 'Professional tennis court with clay surface. Suitable for singles and doubles matches.',
                'hourly_rate' => 35.00,
                'capacity' => 4,
                'status' => 'Active',
                'opening_time' => '08:00',
                'closing_time' => '22:00',
                'min_booking_hours' => 1,
                'max_booking_hours' => 4,
                'amenities' => ['Lighting', 'Parking', 'Equipment Available'],
            ],
            [
                'name' => 'Volleyball Court',
                'type' => 'Volleyball',
                'description' => 'Beach volleyball court with sand surface. Great for recreational and competitive play.',
                'hourly_rate' => 40.00,
                'capacity' => 12,
                'status' => 'Active',
                'opening_time' => '08:00',
                'closing_time' => '22:00',
                'min_booking_hours' => 1,
                'max_booking_hours' => 6,
                'amenities' => ['Lighting', 'Parking', 'Restrooms'],
            ],
            [
                'name' => 'Multi-Purpose Field',
                'type' => 'Multi-Purpose',
                'description' => 'Versatile field suitable for various sports and activities. Artificial turf surface.',
                'hourly_rate' => 55.00,
                'capacity' => 30,
                'status' => 'Active',
                'opening_time' => '08:00',
                'closing_time' => '22:00',
                'min_booking_hours' => 1,
                'max_booking_hours' => 8,
                'amenities' => ['Lighting', 'Parking', 'Restrooms', 'Equipment Available', 'Spectator Seating'],
            ],
            [
                'name' => 'Basketball Court 2',
                'type' => 'Basketball',
                'description' => 'Outdoor basketball court with concrete surface. Half-court setup.',
                'hourly_rate' => 25.00,
                'capacity' => 6,
                'status' => 'Under Maintenance',
                'opening_time' => '08:00',
                'closing_time' => '20:00',
                'min_booking_hours' => 1,
                'max_booking_hours' => 4,
                'amenities' => ['Parking'],
            ],
            [
                'name' => 'Soccer Field B',
                'type' => 'Soccer',
                'description' => 'Training soccer field with artificial turf. Ideal for practice sessions.',
                'hourly_rate' => 60.00,
                'capacity' => 22,
                'status' => 'Active',
                'opening_time' => '08:00',
                'closing_time' => '22:00',
                'min_booking_hours' => 1,
                'max_booking_hours' => 8,
                'amenities' => ['Lighting', 'Parking', 'Restrooms'],
            ],
            [
                'name' => 'Tennis Court B',
                'type' => 'Tennis',
                'description' => 'Hard court tennis surface with professional net system.',
                'hourly_rate' => 30.00,
                'capacity' => 4,
                'status' => 'Inactive',
                'opening_time' => '08:00',
                'closing_time' => '20:00',
                'min_booking_hours' => 1,
                'max_booking_hours' => 4,
                'amenities' => ['Parking', 'Equipment Available'],
            ],
        ];

        foreach ($fields as $fieldData) {
            // Extract amenities from field data
            $amenityNames = $fieldData['amenities'];
            unset($fieldData['amenities']);

            // Create the field
            $field = Field::create($fieldData);

            // Attach amenities using the relationship
            if (!empty($amenityNames)) {
                $amenityIds = Amenity::whereIn('name', $amenityNames)->pluck('id')->toArray();
                if (!empty($amenityIds)) {
                    $field->amenities()->attach($amenityIds);
                }
            }
        }

        $this->command->info('Fields seeded successfully with amenities!');
    }
}
