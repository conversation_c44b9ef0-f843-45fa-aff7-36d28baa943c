<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Amenity;
use Database\Seeders\AmenitySeeder;
use Database\Seeders\FieldSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FieldSeederTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function field_seeder_creates_fields_with_amenities_successfully()
    {
        // First seed amenities
        $this->seed(AmenitySeeder::class);
        
        // Then seed fields
        $this->seed(FieldSeeder::class);

        // Assert fields were created
        $this->assertDatabaseCount('fields', 8);

        // Assert specific fields exist
        $this->assertDatabaseHas('fields', [
            'name' => 'Soccer Field A',
            'type' => 'Soccer',
            'hourly_rate' => 75.00,
            'capacity' => 22,
            'status' => 'Active',
        ]);

        // Assert amenities were attached
        $soccerField = Field::where('name', 'Soccer Field A')->first();
        $this->assertNotNull($soccerField);
        
        $amenityNames = $soccerField->amenities->pluck('name')->toArray();
        $this->assertContains('Lighting', $amenityNames);
        $this->assertContains('Parking', $amenityNames);
        $this->assertContains('Restrooms', $amenityNames);
        $this->assertContains('Spectator Seating', $amenityNames);

        // Assert all fields have required attributes
        $fields = Field::all();
        foreach ($fields as $field) {
            $this->assertNotNull($field->opening_time);
            $this->assertNotNull($field->closing_time);
            $this->assertNotNull($field->min_booking_hours);
            $this->assertNotNull($field->max_booking_hours);
        }
    }

    /** @test */
    public function field_seeder_handles_missing_amenities_gracefully()
    {
        // Seed only some amenities
        Amenity::create(['name' => 'Lighting', 'is_active' => true]);
        Amenity::create(['name' => 'Parking', 'is_active' => true]);
        
        // Seed fields (should work even if some amenities don't exist)
        $this->seed(FieldSeeder::class);

        // Assert fields were still created
        $this->assertDatabaseCount('fields', 8);

        // Assert available amenities were attached
        $soccerField = Field::where('name', 'Soccer Field A')->first();
        $amenityNames = $soccerField->amenities->pluck('name')->toArray();
        $this->assertContains('Lighting', $amenityNames);
        $this->assertContains('Parking', $amenityNames);
        
        // Non-existent amenities should not be attached
        $this->assertNotContains('Restrooms', $amenityNames);
        $this->assertNotContains('Spectator Seating', $amenityNames);
    }
}
